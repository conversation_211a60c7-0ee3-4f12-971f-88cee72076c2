package activity_cashback

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestTaskNamesMapping tests that task names are properly mapped
func TestTaskNamesMapping(t *testing.T) {
	tests := []struct {
		name        string
		taskName    string
		shouldMatch bool
	}{
		// Daily tasks
		{"Daily Check-in", "Daily Check-in", true},
		{"MEME Trade - New Name", "Complete one meme trade", true},
		{"MEME Trade - Old Name", "Complete 1 MEME Trade", true},
		{"Derivatives Trade - New Name", "Complete one derivatives trade", true},
		{"Derivatives Trade - Old Name", "Complete 1 Perpetual Trade", true},
		{"Market View - New Name", "View market page", true},
		{"Market View - Old Name", "Check Market Conditions", true},
		{"Consecutive Trading", "Trade for 3/7/15/30 consecutive days", true},

		// Community tasks
		{"Twitter Follow - New Name", "Follow on X (Twitter)", true},
		{"Twitter Follow - Old Name", "Follow Twitter", true},
		{"Retweet - New Name", "Retweet a post", true},
		{"Retweet - Old Name", "Retweet Post", true},
		{"Like - New Name", "Like a post", true},
		{"Like - Old Name", "Like Post", true},
		{"Invite - New Name", "Invite friends", true},
		{"Invite - Old Name", "Invite Friends", true},
		{"Share - New Name", "Share referral link", true},
		{"Share - Old Name", "Share Referral Link", true},

		// Trading tasks
		{"Trading Points", "Trading Points", true},
		{"Cumulative $10K - New Name", "Cumulative trading $10,000", true},
		{"Cumulative $10K - Old Name", "Accumulated Trading $10,000", true},
		{"Cumulative $50K - New Name", "Cumulative trading $50,000", true},
		{"Cumulative $50K - Old Name", "Accumulated Trading $50,000", true},
		{"Cumulative $100K - New Name", "Cumulative trading $100,000", true},
		{"Cumulative $100K - Old Name", "Accumulated Trading $100,000", true},
		{"Cumulative $500K - New Name", "Cumulative trading $500,000", true},
		{"Cumulative $500K - Old Name", "Accumulated Trading $500,000", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This test validates that our task names are consistent
			assert.NotEmpty(t, tt.taskName, "Task name should not be empty")
			if tt.shouldMatch {
				assert.True(t, len(tt.taskName) > 0, "Valid task names should have content")
			}
		})
	}
}

// TestTradingPointsCalculation tests the trading points calculation logic
func TestTradingPointsCalculation(t *testing.T) {
	tests := []struct {
		name           string
		volume         float64
		tradeType      string
		expectedPoints int
	}{
		// MEME trading (no weight adjustment)
		{"MEME $1", 1.0, "MEME", 1},
		{"MEME $100", 100.0, "MEME", 5},
		{"MEME $500", 500.0, "MEME", 12},
		{"MEME $3000", 3000.0, "MEME", 25},
		{"MEME $10000", 10000.0, "MEME", 40},

		// Derivatives trading (0.5 weight)
		{"Derivatives $2", 2.0, "PERPETUAL", 1},          // 2 * 0.5 = 1
		{"Derivatives $200", 200.0, "PERPETUAL", 5},      // 200 * 0.5 = 100
		{"Derivatives $1000", 1000.0, "PERPETUAL", 12},   // 1000 * 0.5 = 500
		{"Derivatives $6000", 6000.0, "PERPETUAL", 25},   // 6000 * 0.5 = 3000
		{"Derivatives $20000", 20000.0, "PERPETUAL", 40}, // 20000 * 0.5 = 10000

		// Edge cases
		{"Zero volume", 0.0, "MEME", 0},
		{"Very small volume", 0.5, "MEME", 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Apply weight based on trade type
			weightedVolume := tt.volume
			switch tt.tradeType {
			case "PERPETUAL", "DERIVATIVES":
				weightedVolume = tt.volume * 0.5
			case "MEME":
				// No weight adjustment
			}

			// Calculate points based on weighted volume tiers
			var points int
			switch {
			case weightedVolume >= 10000:
				points = 40
			case weightedVolume >= 3000:
				points = 25
			case weightedVolume >= 500:
				points = 12
			case weightedVolume >= 100:
				points = 5
			case weightedVolume >= 1:
				points = 1
			default:
				points = 0
			}

			assert.Equal(t, tt.expectedPoints, points,
				"Points calculation mismatch for volume %.2f, type %s (weighted: %.2f)",
				tt.volume, tt.tradeType, weightedVolume)
		})
	}
}

// TestConsecutiveTradingMilestones tests consecutive trading milestone rewards
func TestConsecutiveTradingMilestones(t *testing.T) {
	milestones := map[int]int{
		3:  50,
		7:  200,
		15: 1000,
		30: 2000,
	}

	for days, expectedPoints := range milestones {
		t.Run(fmt.Sprintf("%d_days", days), func(t *testing.T) {
			assert.Equal(t, expectedPoints, milestones[days],
				"Milestone reward for %d consecutive days should be %d points",
				days, expectedPoints)
		})
	}
}

// TestTaskFrequencyAndTypes tests task frequency configurations
func TestTaskFrequencyAndTypes(t *testing.T) {
	tests := []struct {
		taskName     string
		expectedFreq model.TaskFrequency
	}{
		// Daily tasks
		{"Daily Check-in", model.FrequencyDaily},
		{"Complete one meme trade", model.FrequencyDaily},
		{"Complete one derivatives trade", model.FrequencyDaily},
		{"View market page", model.FrequencyDaily},

		// Community tasks
		{"Follow on X (Twitter)", model.FrequencyOneTime},
		{"Join Telegram", model.FrequencyOneTime},
		{"Invite friends", model.FrequencyUnlimited},
		{"Retweet a post", model.FrequencyUnlimited},
		{"Like a post", model.FrequencyUnlimited},

		// Trading tasks
		{"Trading Points", model.FrequencyDaily},
	}

	for _, tt := range tests {
		t.Run(tt.taskName, func(t *testing.T) {
			// Validate that task frequency is properly defined
			assert.NotEmpty(t, string(tt.expectedFreq), "Task frequency should not be empty")
		})
	}
}
